/* Dashboard Navigation Styles */

/* Navigation */
.dashboard-nav {
  background-color: transparent;
  position: sticky;
  top: 0;
  z-index: 10;
  padding-top: 10px;
  transition: padding-top 0.3s ease-in-out;
}

.dashboard-nav.scrolled {
  padding-top: 5px;
}


.nav-container {
  max-width: 1380px;
  margin: 20px auto 8px auto;
  padding: 0.8rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #0079FF 0%, #004AAD 100%);
  box-shadow: 0 4px 20px rgba(0, 121, 255, 0.3);
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s ease-in-out;
}

/* Scrolled state */
.nav-container.scrolled {
  margin: 10px auto 8px auto;
  max-width: 1000px;
  background: linear-gradient(135deg, #0079FF 0%, #004AAD 100%);
  box-shadow: 0 4px 20px rgba(0, 121, 255, 0.4);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 1rem;
}


.nav-logo {
  font-size: 1.5rem;
  font-weight: 300;
  text-shadow: none;
}

.nav-logo-aurea {
  color: #ffffff; /* white color */
}

.nav-logo-voice {
  color: #fbbf24; /* golden color */
}

/* Navigation buttons */
.nav-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-button {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* Responsive navigation */
@media (max-width: 768px) {
  .nav-container {
    max-width: 95%;
    padding: 0.75rem 1.5rem;
    margin: 0 auto 15px auto;
    justify-content: space-between;
  }

  .nav-container.scrolled {
    max-width: 90%;
  }

  .nav-logo {
    font-size: 1.3rem;
  }

  .nav-logo-aurea {
    color: #ffffff;
  }

  .nav-logo-voice {
    color: #fbbf24;
  }

  .nav-buttons {
    gap: 0.75rem;
  }

  .nav-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    max-width: 98%;
    padding: 0.75rem 1rem;
    margin: 0 auto 10px auto;
  }

  .nav-container.scrolled {
    max-width: 95%;
  }

  .nav-logo {
    font-size: 1.1rem;
  }

  .nav-logo-aurea {
    color: #ffffff;
  }

  .nav-logo-voice {
    color: #fbbf24;
  }

  .nav-buttons {
    gap: 0.5rem;
  }

  .nav-button {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
  }
}
